
                               ____                __         
            ____     __  __   / __/  ____ ___     / /_   ____ 
           / __ \   / / / /  / /_   / __ `__ \   / __/  / __ \
          / /_/ /  / /_/ /  / __/  / / / / / /  / /_   / /_/ /
         / .___/   \__, /  /_/    /_/ /_/ /_/   \__/   \____/ 
        /_/       /____/                                      

INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34           tools.py->line(207)|
=================== IAFFBOClient ==================
╭───────────────┬───────────┬───────────┬─────────╮
│ Parameter     │  Default  │  Updates  │  Using  │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_noise │     -     │    0.0    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ flag_transfer │     1     │     -     │    1    │
├───────────────┼───────────┼───────────┼─────────┤
│ ucb_flag      │     2     │     -     │    2    │
├───────────────┼───────────┼───────────┼─────────┤
│ acq_type      │     -     │    LCB    │    -    │
├───────────────┼───────────┼───────────┼─────────┤
│ privacy_ratio │    0.0    │     -     │   0.0   │
├───────────────┼───────────┼───────────┼─────────┤
│ popsize       │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ max_iter      │    100    │    100    │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ n_samples     │    100    │     -     │   100   │
├───────────────┼───────────┼───────────┼─────────┤
│ phi           │    0.1    │    0.1    │   0.1   │
╰───────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:34        launcher.py->line(168)|
╭───────────┬──────────┬───────────────┬─────────────┬───────────┬───────┬───────────┬────────╮
│  running  │  repeat  │   progress    │  algorithm  │  problem  │  iid  │  clients  │  save  │
├───────────┼──────────┼───────────────┼─────────────┼───────────┼───────┼───────────┼────────┤
│    1/1    │   1/20   │ [1/20][5.00%] │   IAFFBO    │ arxiv2017 │   2   │    18     │  True  │
╰───────────┴──────────┴───────────────┴─────────────┴───────────┴───────┴───────────┴────────╯
INFO    2025-08-04 12:02:34        launcher.py->line(185)|Server started.
INFO    2025-08-04 12:02:35           tools.py->line(207)|
===================== IAFFBOServer ====================
╭───────────────────┬───────────┬───────────┬─────────╮
│ Parameter         │  Default  │  Updates  │  Using  │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_samples         │    100    │    100    │   100   │
├───────────────────┼───────────┼───────────┼─────────┤
│ batch_aggregation │     -     │   True    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ device            │     -     │   cuda    │    -    │
├───────────────────┼───────────┼───────────┼─────────┤
│ n_clusters        │     6     │     6     │    6    │
├───────────────────┼───────────┼───────────┼─────────┤
│ agg_interval      │    0.3    │    0.3    │   0.3   │
╰───────────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 1  started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 2  started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 3  started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 4  started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 2 join, total 1 clients
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 1 join, total 2 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 2  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 3 join, total 3 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 1  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Griewank   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 5  started
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 3  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 5 join, total 4 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 6  started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 7  started
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 5  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 4 join, total 5 clients
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 6 join, total 6 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 8  started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 7 join, total 7 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 9  started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 10 started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 10 join, total 8 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 11 started
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 4  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 8 join, total 9 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 6  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Schwefel   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 11 join, total 10 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 12 started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 9 join, total 11 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 13 started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 14 started
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 10 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rosenbrock │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 15 started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 12 join, total 12 clients
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 13 join, total 13 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 7  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 14 join, total 14 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 16 started
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 17 started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 15 join, total 15 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 8  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Sphere     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 16 join, total 16 clients
INFO    2025-08-04 12:02:36          client.py->line(111)|Client 18 started
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 17 join, total 17 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 11 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 9  Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Ackley     │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
===================== Client 12 Params ====================
╭─────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName    │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├─────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Weierstrass │    10 │     1 │     2 │      50 │     110 │
╰─────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          server.py->line(162)|Client 18 join, total 18 clients
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 13 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rosenbrock │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 14 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 15 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Griewank   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 17 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Rastrigin  │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
===================== Client 16 Params ====================
╭─────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName    │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├─────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Weierstrass │    10 │     1 │     2 │      50 │     110 │
╰─────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36          client.py->line(89)|
==================== Client 18 Params ====================
╭────────────┬───────┬───────┬───────┬─────────┬─────────╮
│ TaskName   │   Dim │   Obj │   IID │   IniFE │   MaxFE │
├────────────┼───────┼───────┼───────┼─────────┼─────────┤
│ Schwefel   │    10 │     1 │     2 │      50 │     110 │
╰────────────┴───────┴───────┴───────┴─────────┴─────────╯
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.015s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.013s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.017s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.017s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.013s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.016s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.010s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.026s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.018s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.007s
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.015s
INFO    2025-08-04 12:02:36   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:36   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:02:51          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.21s] │ [0.01s]           │ [0.01s]                   │ [0.02s] │ [13.09s]          │ [0.01s] │ [1.73s] │ [15.06s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.02s]           │ [0.02s]                   │ [0.04s] │ [13.19s]          │ [0.01s] │ [1.77s] │ [15.17s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:02:51          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.06s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [13.32s]          │ [0.00s] │ [1.87s] │ [15.26s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.02s]           │ [0.03s]                   │ [0.05s] │ [13.25s]          │ [0.01s] │ [1.82s] │ [15.27s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.005s
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.01s]           │ [0.01s]                   │ [0.02s] │ [13.47s]          │ [0.00s] │ [1.84s] │ [15.40s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.00s]                   │ [0.02s] │ [13.40s]          │ [0.01s] │ [1.89s] │ [15.41s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.02s]                   │ [0.03s] │ [13.38s]          │ [0.01s] │ [1.92s] │ [15.41s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.02s]           │ [0.02s]                   │ [0.04s] │ [13.35s]          │ [0.01s] │ [1.91s] │ [15.44s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.13s] │ [0.02s]           │ [0.04s]                   │ [0.07s] │ [13.33s]          │ [0.00s] │ [1.91s] │ [15.45s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.01s]                   │ [0.02s] │ [13.62s]          │ [0.00s] │ [1.96s] │ [15.71s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.14s] │ [0.01s]           │ [0.02s]                   │ [0.03s] │ [13.59s]          │ [0.00s] │ [1.92s] │ [15.68s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.02s]                   │ [0.04s] │ [13.61s]          │ [0.00s] │ [1.92s] │ [15.68s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.01s]           │ [0.03s]                   │ [0.04s] │ [13.61s]          │ [0.00s] │ [1.88s] │ [15.69s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.02s]           │ [0.01s]                   │ [0.03s] │ [13.72s]          │ [0.00s] │ [1.90s] │ [15.72s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [13.73s]          │ [0.00s] │ [1.93s] │ [15.74s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:02:51          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.02s]           │ [0.03s]                   │ [0.04s] │ [13.70s]          │ [0.00s] │ [1.88s] │ [15.78s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:51   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:51   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:52   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
WARNING 2025-08-04 12:02:52   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.013s
INFO    2025-08-04 12:02:52   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:52          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.06s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [13.79s]          │ [0.00s] │ [1.97s] │ [15.85s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-04 12:02:52   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.011s
INFO    2025-08-04 12:02:52   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:02:52          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.16s] │ [0.01s]           │ [0.02s]                   │ [0.03s] │ [13.75s]          │ [0.00s] │ [1.92s] │ [15.86s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-04 12:02:52   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-04 12:02:52   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:52   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:02:52   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:02:52   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-04 12:03:03          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [10.01s]          │ [0.01s] │ [1.86s] │ [11.98s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:03   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:03   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:03:03          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [9.91s]           │ [0.01s] │ [1.83s] │ [11.86s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:03          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [10.04s]          │ [0.01s] │ [1.85s] │ [12.00s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:03   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:03   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:03:03   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:03   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.005s
INFO    2025-08-04 12:03:03          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.15s]          │ [0.01s] │ [2.01s] │ [12.25s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:03          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [9.97s]           │ [0.01s] │ [2.10s] │ [12.20s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:03   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:03   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:03:03   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:03   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:03:03          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.15s]          │ [0.00s] │ [2.14s] │ [12.39s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [9.94s]           │ [0.00s] │ [2.14s] │ [12.16s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [9.94s]           │ [0.01s] │ [2.11s] │ [12.19s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:03:04          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [10.15s]          │ [0.00s] │ [2.18s] │ [12.46s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.04s]          │ [0.00s] │ [2.09s] │ [12.25s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.009s
INFO    2025-08-04 12:03:04          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [10.13s]          │ [0.01s] │ [2.25s] │ [12.51s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:03:04          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.04s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.18s]          │ [0.00s] │ [2.11s] │ [12.35s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.29s]          │ [0.01s] │ [2.26s] │ [12.65s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:03:04          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [9.84s]           │ [0.00s] │ [2.22s] │ [12.22s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-04 12:03:04          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.07s] │ [0.02s]           │ [0.02s]                   │ [0.02s] │ [10.10s]          │ [0.00s] │ [2.18s] │ [12.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [9.98s]           │ [0.00s] │ [2.17s] │ [12.25s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.03s, avg_attempt=0.026s
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.004s
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-04 12:03:04          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [10.03s]          │ [0.00s] │ [2.29s] │ [12.44s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.012s
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:03:04          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.13s]          │ [0.00s] │ [2.20s] │ [12.44s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.004s
INFO    2025-08-04 12:03:04   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:04   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:03:14          client.py->line(103)|
================================================ Client 3  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [9.53s]           │ [0.00s] │ [1.48s] │ [11.11s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:14   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:14   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:03:14          client.py->line(103)|
=============================================== Client 1  Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [9.63s]           │ [0.00s] │ [1.59s] │ [11.35s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:14   iaffbo_client.py->line(232)|Client 1 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:14   iaffbo_client.py->line(267)|Client 1 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.002s
INFO    2025-08-04 12:03:14          client.py->line(103)|
================================================ Client 11 Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [9.66s]           │ [0.00s] │ [1.71s] │ [11.49s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:14   iaffbo_client.py->line(232)|Client 11 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:14   iaffbo_client.py->line(267)|Client 11 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.002s
INFO    2025-08-04 12:03:15          client.py->line(103)|
=============================================== Client 17 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [9.95s]           │ [0.00s] │ [2.04s] │ [12.10s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:15   iaffbo_client.py->line(232)|Client 17 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:15   iaffbo_client.py->line(267)|Client 17 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 7  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.00s]                   │ [0.00s] │ [9.97s]           │ [0.00s] │ [2.17s] │ [12.26s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 7 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 7 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.003s
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 15 Griewank(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.02s]          │ [0.01s] │ [2.24s] │ [12.38s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
============================================== Client 13 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.03s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.01s]          │ [0.00s] │ [2.24s] │ [12.29s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 2  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.15s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [9.90s]           │ [0.00s] │ [2.34s] │ [12.40s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 4  Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.10s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [10.03s]          │ [0.00s] │ [2.35s] │ [12.49s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 14 Rastrigin(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [9.97s]           │ [0.00s] │ [2.32s] │ [12.41s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 15 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 15 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 13 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 13 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 2 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 2 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:03:16          client.py->line(103)|
============================================== Client 12 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.05s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [9.89s]           │ [0.00s] │ [2.32s] │ [12.27s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 4 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 4 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.00s, avg_attempt=0.004s
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 14 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 14 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:03:16          client.py->line(103)|
============================================== Client 10 Rosenbrock(10D) ===============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.11s] │ [0.01s]           │ [0.01s]                   │ [0.01s] │ [9.90s]           │ [0.00s] │ [2.37s] │ [12.39s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 18 Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.01s]           │ [0.03s]                   │ [0.03s] │ [9.97s]           │ [0.00s] │ [2.34s] │ [12.42s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
============================================== Client 16 Weierstrass(10D) ==============================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [9.89s]           │ [0.00s] │ [2.40s] │ [12.39s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
================================================ Client 8  Sphere(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [10.00s]          │ [0.00s] │ [2.40s] │ [12.53s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 12 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 12 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:03:16          client.py->line(103)|
=============================================== Client 6  Schwefel(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.09s] │ [0.00s]           │ [0.01s]                   │ [0.01s] │ [9.86s]           │ [0.00s] │ [2.39s] │ [12.35s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16          client.py->line(103)|
================================================ Client 9  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.08s] │ [0.01s]           │ [0.02s]                   │ [0.02s] │ [9.94s]           │ [0.00s] │ [2.42s] │ [12.46s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 16 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:03:16          client.py->line(103)|
================================================ Client 5  Ackley(10D) =================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬──────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total    │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼──────────┤
│ [0.12s] │ [0.02s]           │ [0.01s]                   │ [0.01s] │ [9.87s]           │ [0.00s] │ [2.37s] │ [12.37s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴──────────╯
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 10 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 16 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.016s
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 18 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 10 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 18 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.008s
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 8 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 8 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.005s
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 6 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 5 starting PULL_WEIGHTS with repeat=1
INFO    2025-08-04 12:03:16   iaffbo_client.py->line(232)|Client 9 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 6 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.010s
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 9 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.007s
WARNING 2025-08-04 12:03:16   iaffbo_client.py->line(267)|Client 5 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.02s, avg_attempt=0.014s
INFO    2025-08-04 12:03:22          client.py->line(103)|
================================================ Client 3  Ackley(10D) ================================================
╭─────────┬───────────────────┬───────────────────────────┬─────────┬───────────────────┬─────────┬─────────┬─────────╮
│ FitGP   │ Pull_X_Hat_Init   │ Pull_Aggregated_Weights   │ Pull    │ TrainClassifier   │ Push    │ CSO     │ Total   │
├─────────┼───────────────────┼───────────────────────────┼─────────┼───────────────────┼─────────┼─────────┼─────────┤
│ [0.09s] │ [0.01s]           │ [0.00s]                   │ [0.00s] │ [7.29s]           │ [0.00s] │ [1.21s] │ [8.59s] │
╰─────────┴───────────────────┴───────────────────────────┴─────────┴───────────────────┴─────────┴─────────┴─────────╯
INFO    2025-08-04 12:03:23   iaffbo_client.py->line(232)|Client 3 starting PULL_WEIGHTS with repeat=1
WARNING 2025-08-04 12:03:23   iaffbo_client.py->line(267)|Client 3 PULL_WEIGHTS FAILED after 1 attempts, total_time=0.01s, avg_attempt=0.006s
INFO    2025-08-04 12:03:23          server.py->line(166)|Client 11 quit, remain 17 clients
INFO    2025-08-04 12:03:23          server.py->line(166)|Client 4 quit, remain 16 clients
INFO    2025-08-04 12:03:23          client.py->line(135)|Client 11 exit with available FE = 57
INFO    2025-08-04 12:03:23          server.py->line(166)|Client 13 quit, remain 15 clients
INFO    2025-08-04 12:03:23          server.py->line(166)|Client 3 quit, remain 14 clients
INFO    2025-08-04 12:03:23          client.py->line(135)|Client 13 exit with available FE = 57
INFO    2025-08-04 12:09:33           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:12:12           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:13:33           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:21:31           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:22:21           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:22:21           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:22:21           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    50     │   50    │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
╰──────────────┴───────────┴───────────┴─────────╯
INFO    2025-08-04 12:22:21           tools.py->line(207)|
================== IAFFBOServer ==================
╭──────────────┬───────────┬───────────┬─────────╮
│ Parameter    │  Default  │  Updates  │  Using  │
├──────────────┼───────────┼───────────┼─────────┤
│ agg_interval │    0.3    │    0.3    │   0.3   │
├──────────────┼───────────┼───────────┼─────────┤
│ n_samples    │    100    │    20     │   20    │
├──────────────┼───────────┼───────────┼─────────┤
│ n_clusters   │     6     │     2     │    2    │
╰──────────────┴───────────┴───────────┴─────────╯
